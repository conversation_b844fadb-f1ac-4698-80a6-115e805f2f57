using iChat.Shared.Models;

namespace iChat.Services.Interfaces;

public interface IDocumentStoreService
{
    Task<DocumentStore?> GetDocumentStoreByIdAsync(Guid documentStoreId);
    Task<IEnumerable<DocumentStore>> GetAllDocumentStoresAsync();
    Task<DocumentStore> CreateDocumentStoreAsync(string name, string description);
    Task<DocumentStore> UpdateDocumentStoreAsync(DocumentStore documentStore);
    Task<bool> DeleteDocumentStoreAsync(Guid documentStoreId);
    Task<bool> DocumentStoreExistsAsync(Guid documentStoreId);
    Task<DocumentStore?> GetDocumentStoreByNameAsync(string name);
    Task<IEnumerable<DocumentStore>> SearchDocumentStoresAsync(string searchTerm);
    Task<int> GetDocumentStoreCountAsync();
    Task<int> GetDocumentCountInStoreAsync(Guid documentStoreId);
    Task<DocumentStore?> GetDefaultDocumentStoreAsync();
    Task<DocumentStore> SetDefaultDocumentStoreAsync(Guid documentStoreId);
}
