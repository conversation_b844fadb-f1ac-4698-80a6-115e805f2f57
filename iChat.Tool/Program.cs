using Spectre.Console.Cli;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Serilog;
using iChat.Tool.Commands;
using iChat.Tool.Services;
using iChat.Shared.Data;
using iChat.Services.DependencyInjection;
using DotNetEnv;

namespace iChat.Tool;

class Program
{
    static int Main(string[] args)
    {
        if (File.Exists(".env"))
            Env.Load(".env");
        
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .CreateLogger();

        try
        {
            var host = CreateHostBuilder(args).Build();

            var app = new CommandApp();
            app.Configure(config =>
            {
                config.SetApplicationName("iChat.Tool");
                config.SetApplicationVersion("1.0.0");

                // Add commands
                config.AddCommand<VersionCommand>("version")
                    .WithDescription("Display the current version of iChat.Tool");

                config.AddCommand<InfoCommand>("info")
                    .WithDescription("Display information about how to use iChat.Tool");

                config.AddDelegate<MigrateSettings>("migrate", (context, settings) =>
                {
                    var migrateCommand = new MigrateCommand(host.Services, host.Services.GetRequiredService<IConfiguration>());
                    return migrateCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                })
                .WithDescription("Run database migrations");

                config.AddDelegate<CreateMigrationSettings>("create-migration", (context, settings) =>
                {
                    var createMigrationCommand = new CreateMigrationCommand(host.Services, host.Services.GetRequiredService<IConfiguration>());
                    return createMigrationCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                })
                .WithDescription("Create a new Entity Framework migration");

                config.AddBranch("document", document =>
                {
                    document.SetDescription("Manage documents in the iChat system");

                    document.AddDelegate<DocumentListSettings>("list", (context, settings) =>
                    {
                        var logger = host.Services.GetRequiredService<ILoggerFactory>().CreateLogger<DocumentListCommand>();
                        var documentListCommand = new DocumentListCommand(host.Services, logger);
                        return documentListCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                    })
                    .WithDescription("List all documents with their document store and section count");
                });
            });

            return app.Run(args);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Application terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // Get connection string
                var connectionString = Environment.GetEnvironmentVariable("CONNECTION_STRING")
                    ?? context.Configuration.GetConnectionString("DefaultConnection");

                // Register iChat Services (includes DbContext and all business logic services)
                if (!string.IsNullOrEmpty(connectionString))
                {
                    services.AddIChatServices(connectionString);
                }
                else
                {
                    services.AddIChatServices(options => options.UseInMemoryDatabase("InMemoryDb"));
                }

                // Register CLI-specific services
                services.AddTransient<IMigrationService, MigrationService>();
            });
}
