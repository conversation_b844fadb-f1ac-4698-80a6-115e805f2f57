using Spectre.Console.Cli;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Serilog;
using iChat.Tool.Commands;
using iChat.Tool.Services;
using iChat.Shared.Data;
using iChat.Services.DependencyInjection;
using iChat.Services.Interfaces;
using DotNetEnv;

namespace iChat.Tool;

class Program
{
    static int Main(string[] args)
    {
        if (File.Exists(".env"))
            Env.Load(".env");
        
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .CreateLogger();

        try
        {
            var host = CreateHostBuilder(args).Build();

            var app = new CommandApp();
            app.Configure(config =>
            {
                config.SetApplicationName("iChat.Tool");
                config.SetApplicationVersion("1.0.0");

                // Add commands
                config.AddCommand<VersionCommand>("version")
                    .WithDescription("Display the current version of iChat.Tool");

                config.AddCommand<InfoCommand>("info")
                    .WithDescription("Display information about how to use iChat.Tool");

                config.AddDelegate<MigrateSettings>("migrate", (context, settings) =>
                {
                    var migrateCommand = new MigrateCommand(host.Services, host.Services.GetRequiredService<IConfiguration>());
                    return migrateCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                })
                .WithDescription("Run database migrations");

                config.AddDelegate<CreateMigrationSettings>("create-migration", (context, settings) =>
                {
                    var createMigrationCommand = new CreateMigrationCommand(host.Services, host.Services.GetRequiredService<IConfiguration>());
                    return createMigrationCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                })
                .WithDescription("Create a new Entity Framework migration");

                config.AddBranch("document", document =>
                {
                    document.SetDescription("Manage documents in the iChat system");

                    document.AddDelegate<DocumentListSettings>("list", (context, settings) =>
                    {
                        var documentService = host.Services.GetRequiredService<IDocumentService>();
                        var documentStoreService = host.Services.GetRequiredService<IDocumentStoreService>();
                        var logger = host.Services.GetRequiredService<ILoggerFactory>().CreateLogger<DocumentListCommand>();
                        var documentListCommand = new DocumentListCommand(documentService, documentStoreService, logger);
                        return documentListCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                    })
                    .WithDescription("List all documents with their document store and section count");

                    document.AddDelegate<DocumentCreateCommand.Settings>("create", (context, settings) =>
                    {
                        var documentService = host.Services.GetRequiredService<IDocumentService>();
                        var documentStoreService = host.Services.GetRequiredService<IDocumentStoreService>();
                        var userService = host.Services.GetRequiredService<IUserService>();
                        var logger = host.Services.GetRequiredService<ILoggerFactory>().CreateLogger<DocumentCreateCommand>();
                        var documentCreateCommand = new DocumentCreateCommand(documentService, documentStoreService, userService, logger);
                        return documentCreateCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                    })
                    .WithDescription("Create a new document from a file");

                    document.AddBranch("store", store =>
                    {
                        store.SetDescription("Manage document stores");

                        store.AddDelegate("list", (context) =>
                        {
                            var documentStoreService = host.Services.GetRequiredService<IDocumentStoreService>();
                            var logger = host.Services.GetRequiredService<ILoggerFactory>().CreateLogger<DocumentStoreListCommand>();
                            var storeListCommand = new DocumentStoreListCommand(documentStoreService, logger);
                            return storeListCommand.ExecuteAsync(context).GetAwaiter().GetResult();
                        })
                        .WithDescription("List all document stores");

                        store.AddDelegate<DocumentStoreCreateCommand.Settings>("create", (context, settings) =>
                        {
                            var documentStoreService = host.Services.GetRequiredService<IDocumentStoreService>();
                            var logger = host.Services.GetRequiredService<ILoggerFactory>().CreateLogger<DocumentStoreCreateCommand>();
                            var storeCreateCommand = new DocumentStoreCreateCommand(documentStoreService, logger);
                            return storeCreateCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                        })
                        .WithDescription("Create a new document store");

                        store.AddDelegate<DocumentStoreSetDefaultCommand.Settings>("set-default", (context, settings) =>
                        {
                            var documentStoreService = host.Services.GetRequiredService<IDocumentStoreService>();
                            var logger = host.Services.GetRequiredService<ILoggerFactory>().CreateLogger<DocumentStoreSetDefaultCommand>();
                            var setDefaultCommand = new DocumentStoreSetDefaultCommand(documentStoreService, logger);
                            return setDefaultCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                        })
                        .WithDescription("Set the default document store");

                        store.AddDelegate<DocumentStoreSearchCommand.Settings>("search", (context, settings) =>
                        {
                            var documentStoreService = host.Services.GetRequiredService<IDocumentStoreService>();
                            var logger = host.Services.GetRequiredService<ILoggerFactory>().CreateLogger<DocumentStoreSearchCommand>();
                            var searchCommand = new DocumentStoreSearchCommand(documentStoreService, logger);
                            return searchCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                        })
                        .WithDescription("Search document stores");
                    });
                });

                config.AddBranch("user", user =>
                {
                    user.SetDescription("Manage users in the iChat system");

                    user.AddDelegate("list", (context) =>
                    {
                        var userService = host.Services.GetRequiredService<IUserService>();
                        var userListCommand = new UserListCommand(userService);
                        return userListCommand.ExecuteAsync(context).GetAwaiter().GetResult();
                    })
                    .WithDescription("List all users");

                    user.AddDelegate<UserCreateCommand.Settings>("create", (context, settings) =>
                    {
                        var userService = host.Services.GetRequiredService<IUserService>();
                        var userCreateCommand = new UserCreateCommand(userService);
                        return userCreateCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                    })
                    .WithDescription("Create a new user");

                    user.AddDelegate<UserSearchCommand.Settings>("search", (context, settings) =>
                    {
                        var userService = host.Services.GetRequiredService<IUserService>();
                        var userSearchCommand = new UserSearchCommand(userService);
                        return userSearchCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                    })
                    .WithDescription("Search users by name or email");

                    user.AddDelegate("stats", (context) =>
                    {
                        var userService = host.Services.GetRequiredService<IUserService>();
                        var userStatsCommand = new UserStatsCommand(userService);
                        return userStatsCommand.ExecuteAsync(context).GetAwaiter().GetResult();
                    })
                    .WithDescription("Get user statistics");
                });
            });

            return app.Run(args);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Application terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // Get connection string
                var connectionString = Environment.GetEnvironmentVariable("CONNECTION_STRING")
                    ?? context.Configuration.GetConnectionString("DefaultConnection");

                // Register iChat Services (includes DbContext and all business logic services)
                if (!string.IsNullOrEmpty(connectionString))
                {
                    services.AddIChatServices(connectionString);
                }
                else
                {
                    services.AddIChatServices(options => options.UseInMemoryDatabase("InMemoryDb"));
                }

                // Register CLI-specific services
                services.AddTransient<IMigrationService, MigrationService>();
            });
}
