using Spectre.Console;
using Spectre.Console.Cli;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using iChat.Shared.Data;
using iChat.Shared.Models;
using System.ComponentModel;
using System.Linq;

namespace iChat.Tool.Commands;

public class DocumentListSettings : CommandSettings
{
    [CommandOption("--store")]
    [Description("Filter documents by document store name")]
    public string? StoreName { get; set; }

    [CommandOption("--limit")]
    [Description("Maximum number of documents to display")]
    public int? Limit { get; set; }
}

public class DocumentListCommand : AsyncCommand<DocumentListSettings>
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DocumentListCommand> _logger;

    public DocumentListCommand(IServiceProvider serviceProvider, ILogger<DocumentListCommand> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public override async Task<int> ExecuteAsync(CommandContext context, DocumentListSettings settings)
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        try
        {
            _logger.LogInformation("Fetching documents from database...");

            var query = dbContext.Documents
                .Include(d => d.DocumentStore)
                .Include(d => d.DocumentSections)
                .AsQueryable();

            // Apply store filter if specified
            if (!string.IsNullOrEmpty(settings.StoreName))
            {
                query = query.Where(d => d.DocumentStore.Name.Contains(settings.StoreName));
                _logger.LogInformation($"Filtering by document store containing: {settings.StoreName}");
            }

            // Apply limit if specified
            if (settings.Limit.HasValue)
            {
                query = query.Take(settings.Limit.Value);
                _logger.LogInformation($"Limiting results to: {settings.Limit.Value}");
            }

            var documents = await query.ToListAsync();

            if (!documents.Any())
            {
                AnsiConsole.MarkupLine("[yellow]No documents found.[/]");
                if (!string.IsNullOrEmpty(settings.StoreName))
                {
                    AnsiConsole.MarkupLine($"[dim]No documents found for store containing: {settings.StoreName}[/]");
                }
                return 0;
            }

            AnsiConsole.MarkupLine($"[green]Found {documents.Count} document(s)[/]");

            // Create a table to display the documents
            var table = new Table();
            table.Title = new TableTitle("[yellow]DOCUMENTS[/]");
            table.AddColumn("ID");
            table.AddColumn("Title");
            table.AddColumn("Document Store");
            table.AddColumn("Sections");
            table.AddColumn("Created");
            table.AddColumn("Updated");

            foreach (var document in documents)
            {
                table.AddRow(
                    document.Id.ToString(),
                    Markup.Escape(document.Title),
                    Markup.Escape(document.DocumentStore.Name),
                    document.DocumentSections.Count.ToString(),
                    document.CreatedAt.ToString("yyyy-MM-dd HH:mm"),
                    document.UpdatedAt.ToString("yyyy-MM-dd HH:mm")
                );
            }

            AnsiConsole.Write(table);

            // Show summary statistics
            var totalSections = documents.Sum(d => d.DocumentSections.Count);
            var storesCount = documents.Select(d => d.DocumentStoreId).Distinct().Count();

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine($"[dim]Summary: {documents.Count} documents across {storesCount} store(s) with {totalSections} total sections[/]");

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list documents");
            AnsiConsole.MarkupLine($"[red]❌ Failed to list documents: {ex.Message}[/]");
            return 1;
        }
    }
}