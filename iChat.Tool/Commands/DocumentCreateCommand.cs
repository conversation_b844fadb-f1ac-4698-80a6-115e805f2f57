using Spectre.Console;
using Spectre.Console.Cli;
using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Shared.Models;
using System.ComponentModel;

namespace iChat.Tool.Commands;

[Description("Create a new document")]
public class DocumentCreateCommand : AsyncCommand<DocumentCreateCommand.Settings>
{
    private readonly IDocumentService _documentService;
    private readonly IDocumentStoreService _documentStoreService;
    private readonly IUserService _userService;
    private readonly ILogger<DocumentCreateCommand> _logger;

    public DocumentCreateCommand(
        IDocumentService documentService,
        IDocumentStoreService documentStoreService,
        IUserService userService,
        ILogger<DocumentCreateCommand> logger)
    {
        _documentService = documentService;
        _documentStoreService = documentStoreService;
        _userService = userService;
        _logger = logger;
    }

    public class Settings : CommandSettings
    {
        [CommandArgument(0, "<TITLE>")]
        [Description("Document title")]
        public string Title { get; set; } = string.Empty;

        [CommandArgument(1, "<FILE_PATH>")]
        [Description("Path to the document file")]
        public string FilePath { get; set; } = string.Empty;

        [CommandOption("-u|--user")]
        [Description("User email who is uploading the document")]
        public string? UserEmail { get; set; }

        [CommandOption("-s|--store")]
        [Description("Document store name (uses default if not specified)")]
        public string? StoreName { get; set; }

        [CommandOption("-m|--mime-type")]
        [Description("MIME type of the document (auto-detected if not specified)")]
        public string? MimeType { get; set; }
    }

    public override async Task<int> ExecuteAsync(CommandContext context, Settings settings)
    {
        try
        {
            // Validate file exists
            if (!File.Exists(settings.FilePath))
            {
                AnsiConsole.MarkupLine($"[red]File not found: {settings.FilePath}[/]");
                return 1;
            }

            // Get or create user
            var user = await GetOrCreateUserAsync(settings.UserEmail);
            if (user == null)
            {
                AnsiConsole.MarkupLine("[red]Failed to get or create user[/]");
                return 1;
            }

            // Get document store
            var documentStore = await GetDocumentStoreAsync(settings.StoreName);
            if (documentStore == null)
            {
                AnsiConsole.MarkupLine("[red]Failed to get document store[/]");
                return 1;
            }

            // Read file content
            var content = await File.ReadAllTextAsync(settings.FilePath);
            
            // Determine MIME type
            var mimeType = settings.MimeType ?? GetMimeType(settings.FilePath);

            // Create document
            AnsiConsole.MarkupLine("[blue]Creating document...[/]");
            
            var document = await _documentService.UploadDocumentAsync(
                settings.Title,
                content,
                settings.FilePath,
                mimeType,
                user.Id,
                documentStore.Id);

            AnsiConsole.MarkupLine($"[green]✅ Document created successfully![/]");
            AnsiConsole.MarkupLine($"  ID: {document.Id}");
            AnsiConsole.MarkupLine($"  Title: {document.Title}");
            AnsiConsole.MarkupLine($"  Store: {documentStore.Name}");
            AnsiConsole.MarkupLine($"  Size: {content.Length:N0} characters");
            AnsiConsole.MarkupLine($"  MIME Type: {document.MimeType}");

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create document");
            AnsiConsole.MarkupLine($"[red]❌ Failed to create document: {ex.Message}[/]");
            return 1;
        }
    }

    private async Task<User?> GetOrCreateUserAsync(string? userEmail)
    {
        if (string.IsNullOrEmpty(userEmail))
        {
            userEmail = AnsiConsole.Ask<string>("Enter user email:");
        }

        var user = await _userService.GetUserByEmailAsync(userEmail);
        if (user == null)
        {
            var createUser = AnsiConsole.Confirm($"User '{userEmail}' not found. Create new user?");
            if (createUser)
            {
                var userName = AnsiConsole.Ask<string>("Enter user name:");
                user = await _userService.CreateUserAsync(userEmail, userName);
                AnsiConsole.MarkupLine($"[green]Created new user: {user.Name}[/]");
            }
        }

        return user;
    }

    private async Task<DocumentStore?> GetDocumentStoreAsync(string? storeName)
    {
        if (string.IsNullOrEmpty(storeName))
        {
            var defaultStore = await _documentStoreService.GetDefaultDocumentStoreAsync();
            if (defaultStore != null)
            {
                return defaultStore;
            }

            // If no default store, list available stores
            var stores = await _documentStoreService.GetAllDocumentStoresAsync();
            var storeList = stores.ToList();

            if (!storeList.Any())
            {
                var createStore = AnsiConsole.Confirm("No document stores found. Create a default store?");
                if (createStore)
                {
                    var newStore = await _documentStoreService.CreateDocumentStoreAsync("Default", "Default document store");
                    await _documentStoreService.SetDefaultDocumentStoreAsync(newStore.Id);
                    return newStore;
                }
                return null;
            }

            var storeNames = storeList.Select(s => s.Name).ToArray();
            var selectedStore = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("Select a document store:")
                    .AddChoices(storeNames));

            return storeList.First(s => s.Name == selectedStore);
        }

        var store = await _documentStoreService.GetDocumentStoreByNameAsync(storeName);
        if (store == null)
        {
            AnsiConsole.MarkupLine($"[yellow]Document store '{storeName}' not found[/]");
        }

        return store;
    }

    private static string GetMimeType(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension switch
        {
            ".txt" => "text/plain",
            ".md" => "text/markdown",
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".json" => "application/json",
            ".xml" => "application/xml",
            ".html" => "text/html",
            ".htm" => "text/html",
            _ => "application/octet-stream"
        };
    }
}
