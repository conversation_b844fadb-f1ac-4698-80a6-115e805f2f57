using Spectre.Console;
using Spectre.Console.Cli;
using Microsoft.Extensions.Logging;
using iChat.Services.Interfaces;
using iChat.Shared.Models;
using System.ComponentModel;

namespace iChat.Tool.Commands;

[Description("List all document stores")]
public class DocumentStoreListCommand : AsyncCommand
{
    private readonly IDocumentStoreService _documentStoreService;
    private readonly ILogger<DocumentStoreListCommand> _logger;

    public DocumentStoreListCommand(IDocumentStoreService documentStoreService, ILogger<DocumentStoreListCommand> logger)
    {
        _documentStoreService = documentStoreService;
        _logger = logger;
    }

    public override async Task<int> ExecuteAsync(CommandContext context)
    {
        try
        {
            var stores = await _documentStoreService.GetAllDocumentStoresAsync();
            var storeList = stores.ToList();

            if (!storeList.Any())
            {
                AnsiConsole.MarkupLine("[yellow]No document stores found.[/]");
                return 0;
            }

            var table = new Table();
            table.Title = new TableTitle("[yellow]DOCUMENT STORES[/]");
            table.AddColumn("ID");
            table.AddColumn("Name");
            table.AddColumn("Description");
            table.AddColumn("Default");
            table.AddColumn("Documents");
            table.AddColumn("Created");

            foreach (var store in storeList)
            {
                var documentCount = await _documentStoreService.GetDocumentCountInStoreAsync(store.Id);
                
                table.AddRow(
                    store.Id.ToString()[..8] + "...",
                    Markup.Escape(store.Name),
                    Markup.Escape(store.Description),
                    store.IsDefault ? "[green]Yes[/]" : "[dim]No[/]",
                    documentCount.ToString(),
                    store.CreatedAt.ToString("yyyy-MM-dd HH:mm")
                );
            }

            AnsiConsole.Write(table);

            var totalDocuments = 0;
            foreach (var store in storeList)
            {
                totalDocuments += await _documentStoreService.GetDocumentCountInStoreAsync(store.Id);
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine($"[dim]Summary: {storeList.Count} store(s) with {totalDocuments} total documents[/]");

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list document stores");
            AnsiConsole.MarkupLine($"[red]❌ Failed to list document stores: {ex.Message}[/]");
            return 1;
        }
    }
}

[Description("Create a new document store")]
public class DocumentStoreCreateCommand : AsyncCommand<DocumentStoreCreateCommand.Settings>
{
    private readonly IDocumentStoreService _documentStoreService;
    private readonly ILogger<DocumentStoreCreateCommand> _logger;

    public DocumentStoreCreateCommand(IDocumentStoreService documentStoreService, ILogger<DocumentStoreCreateCommand> logger)
    {
        _documentStoreService = documentStoreService;
        _logger = logger;
    }

    public class Settings : CommandSettings
    {
        [CommandArgument(0, "<NAME>")]
        [Description("Document store name")]
        public string Name { get; set; } = string.Empty;

        [CommandArgument(1, "<DESCRIPTION>")]
        [Description("Document store description")]
        public string Description { get; set; } = string.Empty;

        [CommandOption("--default")]
        [Description("Set as the default document store")]
        public bool SetAsDefault { get; set; }
    }

    public override async Task<int> ExecuteAsync(CommandContext context, Settings settings)
    {
        try
        {
            var store = await _documentStoreService.CreateDocumentStoreAsync(settings.Name, settings.Description);

            if (settings.SetAsDefault)
            {
                await _documentStoreService.SetDefaultDocumentStoreAsync(store.Id);
                AnsiConsole.MarkupLine($"[green]✅ Document store created and set as default![/]");
            }
            else
            {
                AnsiConsole.MarkupLine($"[green]✅ Document store created successfully![/]");
            }

            AnsiConsole.MarkupLine($"  ID: {store.Id}");
            AnsiConsole.MarkupLine($"  Name: {store.Name}");
            AnsiConsole.MarkupLine($"  Description: {store.Description}");
            AnsiConsole.MarkupLine($"  Default: {(store.IsDefault ? "Yes" : "No")}");

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create document store");
            AnsiConsole.MarkupLine($"[red]❌ Failed to create document store: {ex.Message}[/]");
            return 1;
        }
    }
}

[Description("Set the default document store")]
public class DocumentStoreSetDefaultCommand : AsyncCommand<DocumentStoreSetDefaultCommand.Settings>
{
    private readonly IDocumentStoreService _documentStoreService;
    private readonly ILogger<DocumentStoreSetDefaultCommand> _logger;

    public DocumentStoreSetDefaultCommand(IDocumentStoreService documentStoreService, ILogger<DocumentStoreSetDefaultCommand> logger)
    {
        _documentStoreService = documentStoreService;
        _logger = logger;
    }

    public class Settings : CommandSettings
    {
        [CommandArgument(0, "<NAME>")]
        [Description("Document store name to set as default")]
        public string Name { get; set; } = string.Empty;
    }

    public override async Task<int> ExecuteAsync(CommandContext context, Settings settings)
    {
        try
        {
            var store = await _documentStoreService.GetDocumentStoreByNameAsync(settings.Name);
            if (store == null)
            {
                AnsiConsole.MarkupLine($"[red]Document store '{settings.Name}' not found[/]");
                return 1;
            }

            await _documentStoreService.SetDefaultDocumentStoreAsync(store.Id);

            AnsiConsole.MarkupLine($"[green]✅ Set '{store.Name}' as the default document store[/]");

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set default document store");
            AnsiConsole.MarkupLine($"[red]❌ Failed to set default document store: {ex.Message}[/]");
            return 1;
        }
    }
}

[Description("Search document stores")]
public class DocumentStoreSearchCommand : AsyncCommand<DocumentStoreSearchCommand.Settings>
{
    private readonly IDocumentStoreService _documentStoreService;
    private readonly ILogger<DocumentStoreSearchCommand> _logger;

    public DocumentStoreSearchCommand(IDocumentStoreService documentStoreService, ILogger<DocumentStoreSearchCommand> logger)
    {
        _documentStoreService = documentStoreService;
        _logger = logger;
    }

    public class Settings : CommandSettings
    {
        [CommandArgument(0, "<SEARCH_TERM>")]
        [Description("Search term to find document stores")]
        public string SearchTerm { get; set; } = string.Empty;
    }

    public override async Task<int> ExecuteAsync(CommandContext context, Settings settings)
    {
        try
        {
            var stores = await _documentStoreService.SearchDocumentStoresAsync(settings.SearchTerm);
            var storeList = stores.ToList();

            if (!storeList.Any())
            {
                AnsiConsole.MarkupLine($"[yellow]No document stores found matching '{settings.SearchTerm}'[/]");
                return 0;
            }

            var table = new Table();
            table.Title = new TableTitle($"[yellow]DOCUMENT STORES MATCHING '{settings.SearchTerm}'[/]");
            table.AddColumn("ID");
            table.AddColumn("Name");
            table.AddColumn("Description");
            table.AddColumn("Default");
            table.AddColumn("Documents");

            foreach (var store in storeList)
            {
                var documentCount = await _documentStoreService.GetDocumentCountInStoreAsync(store.Id);
                
                table.AddRow(
                    store.Id.ToString()[..8] + "...",
                    Markup.Escape(store.Name),
                    Markup.Escape(store.Description),
                    store.IsDefault ? "[green]Yes[/]" : "[dim]No[/]",
                    documentCount.ToString()
                );
            }

            AnsiConsole.Write(table);

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search document stores");
            AnsiConsole.MarkupLine($"[red]❌ Failed to search document stores: {ex.Message}[/]");
            return 1;
        }
    }
}
